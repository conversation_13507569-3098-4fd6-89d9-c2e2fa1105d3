import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading2,
  Heading3,
  Button,
  StatusBadge,
  HealthBadge,
} from '@/components/ui';

interface CheckupRecord {
  id: string;
  gula_darah: number;
  tekanan_darah_sistolik: number;
  tekanan_darah_diastolik: number;
  berat_badan: number;
  tinggi_badan: number;
  keluhan?: string;
  catatan?: string;
  created_at: string;
  petugas_nama?: string;
}

interface ProfileData {
  id: string;
  nama: string;
  usia: number;
  alamat: string;
  no_telepon: string;
}

interface HistoryData {
  profile: ProfileData;
  checkups: CheckupRecord[];
  stats: {
    totalCheckups: number;
    avgGulaDarah: number;
    avgSistolik: number;
    avgDiastolik: number;
    lastBMI: number;
  };
}

export default function ProfileHistoryScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { id } = useLocalSearchParams<{ id: string }>();
  const [historyData, setHistoryData] = useState<HistoryData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadHistoryData = useCallback(async (isRefresh = false) => {
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const response = await apiHelpers.getProfileHistory(id);

      if (response.success && response.data) {
        setHistoryData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Memuat Riwayat',
          text2: response.message || 'Tidak dapat memuat riwayat pemeriksaan',
        });
      }
    } catch (error: any) {
      console.error('History load error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Memuat Riwayat',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      loadHistoryData();
    }
  }, [id, loadHistoryData]);

  const handleRefresh = () => {
    loadHistoryData(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const calculateBMI = (weight: number, height: number): number => {
    if (weight <= 0 || height <= 0) return 0;
    const heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  };

  const parseBloodPressure = (tekananDarah: string): { systolic: number; diastolic: number } => {
    if (!tekananDarah || typeof tekananDarah !== 'string') {
      return { systolic: 0, diastolic: 0 };
    }

    const parts = tekananDarah.split('/');
    if (parts.length !== 2) {
      return { systolic: 0, diastolic: 0 };
    }

    const systolic = parseInt(parts[0]) || 0;
    const diastolic = parseInt(parts[1]) || 0;

    return { systolic, diastolic };
  };

  const getBMIStatus = (bmi: number): 'good' | 'warning' | 'danger' | 'neutral' => {
    if (bmi === 0) return 'neutral';
    if (bmi < 18.5) return 'warning';
    if (bmi >= 18.5 && bmi < 25) return 'good';
    if (bmi >= 25 && bmi < 30) return 'warning';
    return 'danger';
  };

  const getBMILabel = (bmi: number): string => {
    if (bmi === 0) return 'Belum dihitung';
    if (bmi < 18.5) return 'Kurus';
    if (bmi >= 18.5 && bmi < 25) return 'Normal';
    if (bmi >= 25 && bmi < 30) return 'Gemuk';
    return 'Obesitas';
  };

  if (isLoading && !historyData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Typography variant="body1" align="center">
            Memuat riwayat pemeriksaan...
          </Typography>
        </View>
      </View>
    );
  }

  if (!historyData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Typography variant="body1" align="center" color="muted">
            Riwayat pemeriksaan tidak ditemukan
          </Typography>
          <Button
            title="Kembali"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <View style={styles.header}>
          <Heading2 weight="bold">Riwayat Pemeriksaan</Heading2>
          <Typography variant="body2" color="muted">
            {historyData.profile.nama} • {historyData.profile.usia} tahun
          </Typography>
        </View>

        {/* Profile Summary */}
        <Card variant="outlined" style={styles.profileCard}>
          <CardContent>
            <View style={styles.profileInfo}>
              <Typography variant="h4" weight="semibold">
                {historyData.profile.nama}
              </Typography>
              <Typography variant="body2" color="muted">
                {historyData.profile.alamat}
              </Typography>
              <Typography variant="body2" color="muted">
                {historyData.profile.no_telepon}
              </Typography>
            </View>
          </CardContent>
        </Card>

        {/* Health Statistics */}
        {historyData.stats && (
          <Card variant="elevated" style={styles.statsCard}>
            <CardHeader>
              <Heading3>Ringkasan Kesehatan</Heading3>
            </CardHeader>
            <CardContent>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {historyData.stats.totalCheckups}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Total Pemeriksaan
                  </Typography>
                </View>
                
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {historyData.stats.avgGulaDarah.toFixed(0)}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Rata-rata Gula Darah
                  </Typography>
                </View>
                
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {historyData.stats.avgSistolik.toFixed(0)}/{historyData.stats.avgDiastolik.toFixed(0)}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Rata-rata Tekanan Darah
                  </Typography>
                </View>
                
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {historyData.stats.lastBMI.toFixed(1)}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    BMI Terakhir
                  </Typography>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card variant="outlined" style={styles.actionsCard}>
          <CardContent>
            <View style={styles.actionButtons}>
              <Button
                title="Tambah Pemeriksaan"
                onPress={() => router.push(`/checkup/add?profileId=${id}`)}
                icon={<Ionicons name="add" size={20} color={colors.primaryForeground} />}
                style={styles.actionButton}
              />
              <Button
                title="Lihat Grafik"
                variant="outline"
                onPress={() => router.push(`/profile/${id}/charts`)}
                icon={<Ionicons name="analytics" size={20} color={colors.primary} />}
                style={styles.actionButton}
              />
            </View>
          </CardContent>
        </Card>

        {/* Checkup History */}
        <View style={styles.historySection}>
          <Typography variant="h4" weight="semibold" style={styles.sectionTitle}>
            Riwayat Pemeriksaan ({historyData.checkups.length})
          </Typography>
          
          {historyData.checkups.length === 0 ? (
            <Card variant="outlined" style={styles.emptyCard}>
              <CardContent>
                <View style={styles.emptyContent}>
                  <Ionicons name="medical" size={48} color={colors.textMuted} />
                  <Typography variant="body1" align="center" color="muted">
                    Belum ada riwayat pemeriksaan
                  </Typography>
                  <Typography variant="body2" align="center" color="muted">
                    Tambahkan pemeriksaan pertama untuk mulai melacak kesehatan
                  </Typography>
                  <Button
                    title="Tambah Pemeriksaan"
                    onPress={() => router.push(`/checkup/add?profileId=${id}`)}
                    style={styles.emptyButton}
                  />
                </View>
              </CardContent>
            </Card>
          ) : (
            <View style={styles.checkupList}>
              {historyData.checkups.map((checkup, index) => {
                const bmi = calculateBMI(checkup.berat_badan || 0, checkup.tinggi_badan || 0);
                const bloodPressure = parseBloodPressure(checkup.tekanan_darah);

                return (
                  <Card key={checkup.id} variant="outlined" style={styles.checkupCard}>
                    <CardContent>
                      <View style={styles.checkupHeader}>
                        <Typography variant="body1" weight="semibold">
                          Pemeriksaan #{historyData.checkups.length - index}
                        </Typography>
                        <Typography variant="caption" color="muted">
                          {formatDate(checkup.created_at)}
                        </Typography>
                      </View>

                      <View style={styles.checkupData}>
                        <View style={styles.vitalSigns}>
                          <View style={styles.vitalItem}>
                            <Typography variant="caption" color="muted">
                              Gula Darah
                            </Typography>
                            <HealthBadge value={checkup.gula_darah} type="blood_sugar" size="sm" />
                          </View>

                          <View style={styles.vitalItem}>
                            <Typography variant="caption" color="muted">
                              Tekanan Darah
                            </Typography>
                            <HealthBadge
                              value={0}
                              type="blood_pressure"
                              systolic={bloodPressure.systolic}
                              diastolic={bloodPressure.diastolic}
                              size="sm"
                            />
                          </View>

                          <View style={styles.vitalItem}>
                            <Typography variant="caption" color="muted">
                              BMI
                            </Typography>
                            <View style={styles.bmiDisplay}>
                              <Typography variant="body2" weight="medium">
                                {bmi > 0 ? bmi.toFixed(1) : 'N/A'}
                              </Typography>
                              <StatusBadge
                                status={getBMIStatus(bmi)}
                                label={getBMILabel(bmi)}
                                size="sm"
                              />
                            </View>
                          </View>
                        </View>

                        <View style={styles.physicalMeasurements}>
                          <Typography variant="caption" color="muted">
                            BB: {checkup.berat_badan || 'N/A'} kg • TB: {checkup.tinggi_badan || 'N/A'} cm
                          </Typography>
                        </View>
                        
                        {(checkup.keluhan || checkup.catatan) && (
                          <View style={styles.additionalInfo}>
                            {checkup.keluhan && (
                              <View style={styles.infoItem}>
                                <Typography variant="caption" color="muted">
                                  Keluhan:
                                </Typography>
                                <Typography variant="body2">
                                  {checkup.keluhan}
                                </Typography>
                              </View>
                            )}
                            {checkup.catatan && (
                              <View style={styles.infoItem}>
                                <Typography variant="caption" color="muted">
                                  Catatan:
                                </Typography>
                                <Typography variant="body2">
                                  {checkup.catatan}
                                </Typography>
                              </View>
                            )}
                          </View>
                        )}
                        
                        {checkup.petugas_nama && (
                          <Typography variant="caption" color="muted" style={styles.petugasInfo}>
                            Petugas: {checkup.petugas_nama}
                          </Typography>
                        )}
                      </View>
                    </CardContent>
                  </Card>
                );
              })}
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  backButton: {
    minWidth: 120,
  },
  header: {
    marginBottom: 24,
  },
  profileCard: {
    marginBottom: 20,
  },
  profileInfo: {
    gap: 4,
  },
  statsCard: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
  },
  actionsCard: {
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  historySection: {
    gap: 16,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  emptyCard: {
    padding: 20,
  },
  emptyContent: {
    alignItems: 'center',
    gap: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  checkupList: {
    gap: 12,
  },
  checkupCard: {
    marginBottom: 8,
  },
  checkupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkupData: {
    gap: 12,
  },
  vitalSigns: {
    gap: 8,
  },
  vitalItem: {
    gap: 4,
  },
  bmiDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  physicalMeasurements: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  additionalInfo: {
    gap: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  infoItem: {
    gap: 2,
  },
  petugasInfo: {
    marginTop: 8,
    fontStyle: 'italic',
  },
});
