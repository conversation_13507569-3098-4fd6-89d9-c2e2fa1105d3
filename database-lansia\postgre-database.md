# Database Setup Guide

## Prerequisites
- MySQL Server installed and running
- MySQL credentials: username: `root`, password: `root`
- Database name: `app_lansia`

## Setup Instructions

### 1. Create Database and Tables
```bash
mysql -u root -p < schema.sql
```

### 2. Verify Installation
```sql
USE app_lansia;
SHOW TABLES;
DESCRIBE users;
DESCRIBE profiles;
DESCRIBE checkups;
```

### 3. Test Sample Data
```sql
SELECT * FROM profiles;
SELECT * FROM checkups;
SELECT * FROM profile_with_latest_checkup;
```

## Database Schema Overview

### Tables
- **users**: Admin authentication and authorization
- **profiles**: Elderly person profiles with QR codes
- **checkups**: Health examination records

### Views
- **profile_with_latest_checkup**: Profiles with their most recent checkup
- **checkup_statistics**: Statistical summary per profile

### Stored Procedures
- **GetProfileWithHistory**: Get profile with all checkup history
- **AddCheckupWithValidation**: Add checkup with data validation

### Security Features
- Password hashing for admin users
- Input validation with CHECK constraints
- Foreign key constraints with CASCADE delete
- Proper indexing for performance

## Connection Details
```javascript
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'app_lansia',
  port: 3306
};
```
