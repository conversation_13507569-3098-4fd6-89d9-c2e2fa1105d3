import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading2,
  Button,
  Input,
} from '@/components/ui';

const profileSchema: yup.ObjectSchema<ProfileFormData> = yup.object({
  nama: yup
    .string()
    .required('Nama wajib diisi')
    .min(2, 'Nama minimal 2 karakter'),
  usia: yup
    .number()
    .required('Usia wajib diisi')
    .min(1, 'Usia minimal 1 tahun')
    .max(150, 'Usia maksimal 150 tahun'),
  alamat: yup
    .string()
    .required('Alamat wajib diisi')
    .min(5, 'Alamat minimal 5 karakter'),
  no_telepon: yup
    .string()
    .required('No. telepon wajib diisi')
    .matches(/^[0-9+\-\s()]+$/, 'Format nomor telepon tidak valid'),
  kontak_darurat: yup
    .string()
    .required('Kontak darurat wajib diisi')
    .matches(/^[0-9+\-\s()]+$/, 'Format nomor telepon tidak valid'),
  riwayat_penyakit: yup.string().optional(),
  obat_rutin: yup.string().optional(),
  alergi: yup.string().optional(),
});

interface ProfileFormData {
  nama: string;
  usia: number;
  alamat: string;
  no_telepon: string;
  kontak_darurat: string;
  riwayat_penyakit?: string;
  obat_rutin?: string;
  alergi?: string;
}

export default function AddProfileScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProfileFormData>({
    resolver: yupResolver(profileSchema),
    defaultValues: {
      nama: '',
      usia: 0,
      alamat: '',
      no_telepon: '',
      kontak_darurat: '',
      riwayat_penyakit: undefined,
      obat_rutin: undefined,
      alergi: undefined,
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    setIsSubmitting(true);
    try {
      const response = await apiHelpers.createProfile(data);
      
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Profil Berhasil Dibuat',
          text2: `Profil ${data.nama} telah ditambahkan`,
        });
        
        // Show success alert with options
        Alert.alert(
          'Profil Berhasil Dibuat',
          `Profil ${data.nama} telah berhasil ditambahkan ke sistem.`,
          [
            {
              text: 'Tambah Lagi',
              onPress: () => reset(),
            },
            {
              text: 'Lihat Profil',
              onPress: () => router.replace(`/profile/${response.data.id}`),
            },
            {
              text: 'Kembali',
              onPress: () => router.back(),
              style: 'cancel',
            },
          ]
        );
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Membuat Profil',
          text2: response.message || 'Terjadi kesalahan saat menyimpan data',
        });
      }
    } catch (error: any) {
      console.error('Profile creation error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Membuat Profil',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Heading2 weight="bold">Tambah Profil Lansia</Heading2>
          <Typography variant="body2" color="muted">
            Lengkapi data profil lansia untuk pendaftaran
          </Typography>
        </View>

        <Card variant="outlined" style={styles.formCard}>
          <CardHeader>
            <Typography variant="h4" weight="semibold">
              Data Pribadi
            </Typography>
          </CardHeader>
          <CardContent>
            <View style={styles.formSection}>
              <Controller
                control={control}
                name="nama"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Nama Lengkap"
                    placeholder="Masukkan nama lengkap"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={errors.nama?.message}
                    required
                  />
                )}
              />

              <Controller
                control={control}
                name="usia"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Usia"
                    placeholder="Masukkan usia"
                    value={value?.toString() || ''}
                    onChangeText={(text) => onChange(parseInt(text) || 0)}
                    onBlur={onBlur}
                    keyboardType="numeric"
                    error={errors.usia?.message}
                    required
                  />
                )}
              />

              <Controller
                control={control}
                name="alamat"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Alamat"
                    placeholder="Masukkan alamat lengkap"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={3}
                    error={errors.alamat?.message}
                    required
                  />
                )}
              />

              <Controller
                control={control}
                name="no_telepon"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="No. Telepon"
                    placeholder="Masukkan nomor telepon"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    keyboardType="phone-pad"
                    error={errors.no_telepon?.message}
                    required
                  />
                )}
              />

              <Controller
                control={control}
                name="kontak_darurat"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Kontak Darurat"
                    placeholder="Masukkan nomor kontak darurat"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    keyboardType="phone-pad"
                    error={errors.kontak_darurat?.message}
                    required
                  />
                )}
              />
            </View>
          </CardContent>
        </Card>

        <Card variant="outlined" style={styles.formCard}>
          <CardHeader>
            <Typography variant="h4" weight="semibold">
              Informasi Kesehatan
            </Typography>
            <Typography variant="body2" color="muted">
              Opsional - dapat diisi nanti
            </Typography>
          </CardHeader>
          <CardContent>
            <View style={styles.formSection}>
              <Controller
                control={control}
                name="riwayat_penyakit"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Riwayat Penyakit"
                    placeholder="Masukkan riwayat penyakit (opsional)"
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={3}
                    error={errors.riwayat_penyakit?.message}
                  />
                )}
              />

              <Controller
                control={control}
                name="obat_rutin"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Obat Rutin"
                    placeholder="Masukkan obat yang dikonsumsi rutin (opsional)"
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={3}
                    error={errors.obat_rutin?.message}
                  />
                )}
              />

              <Controller
                control={control}
                name="alergi"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Alergi"
                    placeholder="Masukkan informasi alergi (opsional)"
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={2}
                    error={errors.alergi?.message}
                  />
                )}
              />
            </View>
          </CardContent>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Simpan Profil"
            onPress={handleSubmit(onSubmit)}
            loading={isSubmitting}
            style={styles.submitButton}
          />
          <Button
            title="Batal"
            variant="outline"
            onPress={() => router.back()}
            disabled={isSubmitting}
            style={styles.cancelButton}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  header: {
    marginBottom: 24,
  },
  formCard: {
    marginBottom: 20,
  },
  formSection: {
    gap: 16,
  },
  buttonContainer: {
    gap: 12,
    marginTop: 20,
  },
  submitButton: {
    marginBottom: 8,
  },
  cancelButton: {
    marginBottom: 8,
  },
});
