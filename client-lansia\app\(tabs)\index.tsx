import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '@/hooks/useAuth';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading2,
  Heading3,
  Button,
  StatsGrid,
  GridItem,
  QuickStatus,
} from '@/components/ui';
import { QRScanner } from '@/components/QRScanner';
import { UserMenu } from '@/components/UserMenu';

interface DashboardStats {
  overview: {
    totalProfiles: number;
    todayCheckups: number;
    monthlyCheckups: number;
    averageAge: number;
  };
  healthStats: {
    avgGulaDarah: number;
    highSugarCount: number;
    lowSugarCount: number;
  };
  recentActivities: {
    type: string;
    id: string;
    nama: string;
    date: string;
    created_at: string;
  }[];
}

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async (isRefresh = false) => {
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const [statsResponse, alertsResponse] = await Promise.all([
        apiHelpers.getDashboardStats(),
        apiHelpers.getDashboardAlerts()
      ]);

      if (statsResponse.success && statsResponse.data) {
        const combinedData = {
          ...statsResponse.data,
          alerts: alertsResponse.success ? alertsResponse.data : []
        };
        setDashboardData(combinedData);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Memuat Data',
          text2: statsResponse.message || 'Tidak dapat memuat data dashboard',
        });
      }
    } catch (error: any) {
      console.error('Dashboard load error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Memuat Data',
        text2: error.response?.data?.message || 'Terjadi kesalahan jaringan',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadDashboardData(true);
  };

  const handleQRScanSuccess = (profileData: any) => {
    Toast.show({
      type: 'success',
      text1: 'Profil Ditemukan',
      text2: `${profileData.nama} - ${profileData.usia} tahun`,
    });

    // Navigate to profile detail or checkup form
    router.push(`/profile/${profileData.id}`);
  };



  if (isLoading && !dashboardData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Typography variant="body1" align="center">
            Memuat data dashboard...
          </Typography>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Typography variant="h4" color="muted">
              Selamat datang,
            </Typography>
            <Heading2 weight="bold">
              {user?.nama || 'Petugas'}
            </Heading2>
          </View>
          <TouchableOpacity
            style={[styles.userMenuButton, { backgroundColor: colors.primary }]}
            onPress={() => setShowUserMenu(true)}
          >
            <Ionicons name="person" size={24} color={colors.primaryForeground} />
          </TouchableOpacity>
        </View>

        {/* Quick Stats */}
        <Card variant="elevated" style={styles.statsCard}>
          <CardHeader>
            <Heading3>Ringkasan Hari Ini</Heading3>
          </CardHeader>
          <CardContent>
            <StatsGrid>
              <GridItem>
                <QuickStatus
                  count={dashboardData?.overview.totalProfiles || 0}
                  label="Total Lansia"
                  status="neutral"
                />
              </GridItem>
              <GridItem>
                <QuickStatus
                  count={dashboardData?.overview.todayCheckups || 0}
                  label="Pemeriksaan Hari Ini"
                  status="good"
                />
              </GridItem>
              <GridItem>
                <QuickStatus
                  count={dashboardData?.overview.monthlyCheckups || 0}
                  label="Pemeriksaan Bulan Ini"
                  status="neutral"
                />
              </GridItem>
              <GridItem>
                <QuickStatus
                  count={dashboardData?.overview.averageAge || 0}
                  label="Rata-rata Usia"
                  status="neutral"
                />
              </GridItem>
            </StatsGrid>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card variant="outlined" style={styles.actionsCard}>
          <CardHeader>
            <Heading3>Aksi Cepat</Heading3>
          </CardHeader>
          <CardContent>
            <View style={styles.actionButtons}>
              <Button
                title="Pindai QR Code"
                onPress={() => setShowQRScanner(true)}
                icon={<Ionicons name="qr-code" size={20} color={colors.primaryForeground} />}
                style={styles.actionButton}
              />
              <Button
                title="Tambah Profil Baru"
                variant="outline"
                onPress={() => router.push('/profile/add')}
                icon={<Ionicons name="person-add" size={20} color={colors.primary} />}
                style={styles.actionButton}
              />
              <Button
                title="Lihat Semua Profil"
                variant="ghost"
                onPress={() => router.push('/profiles')}
                icon={<Ionicons name="people" size={20} color={colors.text} />}
                style={styles.actionButton}
              />
            </View>
          </CardContent>
        </Card>
      </ScrollView>

      {/* QR Scanner Modal */}
      <QRScanner
        visible={showQRScanner}
        onClose={() => setShowQRScanner(false)}
        onScanSuccess={handleQRScanSuccess}
      />

      {/* User Menu Modal */}
      <UserMenu
        visible={showUserMenu}
        onClose={() => setShowUserMenu(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  qrButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userMenuButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsCard: {
    marginBottom: 20,
  },
  actionsCard: {
    marginBottom: 20,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
});
