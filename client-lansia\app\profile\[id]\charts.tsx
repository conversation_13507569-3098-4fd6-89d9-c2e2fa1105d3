import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'react-native-chart-kit';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading2,
  Heading3,
  Button,
} from '@/components/ui';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 60; // Account for padding

interface ChartData {
  profile: {
    id: string;
    nama: string;
    usia: number;
  };
  bloodSugarTrend: {
    labels: string[];
    datasets: {
      data: number[];
      color?: (opacity: number) => string;
      strokeWidth?: number;
    }[];
  };
  bloodPressureTrend: {
    labels: string[];
    datasets: {
      data: number[];
      color?: (opacity: number) => string;
      strokeWidth?: number;
    }[];
  };
  bmiTrend: {
    labels: string[];
    datasets: {
      data: number[];
      color?: (opacity: number) => string;
      strokeWidth?: number;
    }[];
  };
  monthlyStats: {
    labels: string[];
    datasets: {
      data: number[];
    }[];
  };
}

export default function ProfileChartsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { id } = useLocalSearchParams<{ id: string }>();
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'1m' | '3m' | '6m' | '1y'>('3m');

  const loadChartData = useCallback(async (isRefresh = false) => {
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const response = await apiHelpers.getProfileCharts(id, selectedPeriod);

      if (response.success && response.data) {
        setChartData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Memuat Grafik',
          text2: response.message || 'Tidak dapat memuat data grafik',
        });
      }
    } catch (error: any) {
      console.error('Charts load error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Memuat Grafik',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [id, selectedPeriod]);

  useEffect(() => {
    if (id) {
      loadChartData();
    }
  }, [id, loadChartData]);

  const handleRefresh = () => {
    loadChartData(true);
  };

  const chartConfig = {
    backgroundColor: colors.surface,
    backgroundGradientFrom: colors.surface,
    backgroundGradientTo: colors.surface,
    decimalPlaces: 1,
    color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
    labelColor: () => colors.text,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: colors.primary,
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: colors.border,
      strokeWidth: 1,
    },
  };

  const barChartConfig = {
    ...chartConfig,
    color: (opacity = 1) => `rgba(34, 197, 94, ${opacity})`,
  };

  if (isLoading && !chartData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Typography variant="body1" align="center">
            Memuat grafik kesehatan...
          </Typography>
        </View>
      </View>
    );
  }

  if (!chartData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Typography variant="body1" align="center" color="muted">
            Data grafik tidak tersedia
          </Typography>
          <Typography variant="body2" align="center" color="muted">
            Tambahkan beberapa pemeriksaan untuk melihat tren kesehatan
          </Typography>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <View style={styles.header}>
          <Heading2 weight="bold">Grafik Kesehatan</Heading2>
          <Typography variant="body2" color="muted">
            {chartData.profile.nama} • Tren kesehatan
          </Typography>
        </View>

        {/* Period Selector */}
        <Card variant="outlined" style={styles.periodCard}>
          <CardContent>
            <Typography variant="body2" weight="medium" style={styles.periodLabel}>
              Periode Data
            </Typography>
            <View style={styles.periodButtons}>
              {[
                { key: '1m', label: '1 Bulan' },
                { key: '3m', label: '3 Bulan' },
                { key: '6m', label: '6 Bulan' },
                { key: '1y', label: '1 Tahun' },
              ].map((period) => (
                <Button
                  key={period.key}
                  title={period.label}
                  variant={selectedPeriod === period.key ? 'primary' : 'ghost'}
                  onPress={() => setSelectedPeriod(period.key as any)}
                  style={styles.periodButton}
                />
              ))}
            </View>
          </CardContent>
        </Card>

        {/* Blood Sugar Trend */}
        {chartData.bloodSugarTrend.datasets[0].data.length > 0 && (
          <Card variant="outlined" style={styles.chartCard}>
            <CardHeader>
              <Heading3>Tren Gula Darah</Heading3>
              <Typography variant="body2" color="muted">
                Kadar gula darah dalam mg/dL
              </Typography>
            </CardHeader>
            <CardContent>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <LineChart
                  data={chartData.bloodSugarTrend}
                  width={Math.max(chartWidth, chartData.bloodSugarTrend.labels.length * 60)}
                  height={220}
                  chartConfig={chartConfig}
                  bezier
                  style={styles.chart}
                  withInnerLines={true}
                  withOuterLines={true}
                  withVerticalLines={true}
                  withHorizontalLines={true}
                />
              </ScrollView>
              
              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
                  <Typography variant="caption" color="muted">
                    Normal: 70-140 mg/dL
                  </Typography>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Blood Pressure Trend */}
        {chartData.bloodPressureTrend.datasets[0].data.length > 0 && (
          <Card variant="outlined" style={styles.chartCard}>
            <CardHeader>
              <Heading3>Tren Tekanan Darah</Heading3>
              <Typography variant="body2" color="muted">
                Tekanan darah sistolik dalam mmHg
              </Typography>
            </CardHeader>
            <CardContent>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <LineChart
                  data={chartData.bloodPressureTrend}
                  width={Math.max(chartWidth, chartData.bloodPressureTrend.labels.length * 60)}
                  height={220}
                  chartConfig={{
                    ...chartConfig,
                    color: (opacity = 1) => `rgba(239, 68, 68, ${opacity})`,
                  }}
                  bezier
                  style={styles.chart}
                  withInnerLines={true}
                  withOuterLines={true}
                  withVerticalLines={true}
                  withHorizontalLines={true}
                />
              </ScrollView>
              
              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: '#ef4444' }]} />
                  <Typography variant="caption" color="muted">
                    Normal: &lt;120 mmHg (sistolik)
                  </Typography>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* BMI Trend */}
        {chartData.bmiTrend.datasets[0].data.length > 0 && (
          <Card variant="outlined" style={styles.chartCard}>
            <CardHeader>
              <Heading3>Tren Indeks Massa Tubuh</Heading3>
              <Typography variant="body2" color="muted">
                BMI berdasarkan berat dan tinggi badan
              </Typography>
            </CardHeader>
            <CardContent>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <LineChart
                  data={chartData.bmiTrend}
                  width={Math.max(chartWidth, chartData.bmiTrend.labels.length * 60)}
                  height={220}
                  chartConfig={{
                    ...chartConfig,
                    color: (opacity = 1) => `rgba(168, 85, 247, ${opacity})`,
                  }}
                  bezier
                  style={styles.chart}
                  withInnerLines={true}
                  withOuterLines={true}
                  withVerticalLines={true}
                  withHorizontalLines={true}
                />
              </ScrollView>
              
              <View style={styles.chartLegend}>
                <View style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: '#a855f7' }]} />
                  <Typography variant="caption" color="muted">
                    Normal: 18.5-24.9 BMI
                  </Typography>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Monthly Checkups */}
        {chartData.monthlyStats.datasets[0].data.length > 0 && (
          <Card variant="outlined" style={styles.chartCard}>
            <CardHeader>
              <Heading3>Frekuensi Pemeriksaan</Heading3>
              <Typography variant="body2" color="muted">
                Jumlah pemeriksaan per bulan
              </Typography>
            </CardHeader>
            <CardContent>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <BarChart
                  data={chartData.monthlyStats}
                  width={Math.max(chartWidth, chartData.monthlyStats.labels.length * 80)}
                  height={220}
                  yAxisLabel=""
                  yAxisSuffix=""
                  chartConfig={barChartConfig}
                  style={styles.chart}
                  withInnerLines={true}
                  showValuesOnTopOfBars={true}
                />
              </ScrollView>
            </CardContent>
          </Card>
        )}

        {/* No Data Message */}
        {chartData.bloodSugarTrend.datasets[0].data.length === 0 && (
          <Card variant="outlined" style={styles.noDataCard}>
            <CardContent>
              <View style={styles.noDataContent}>
                <Typography variant="body1" align="center" color="muted">
                  Belum ada data untuk periode ini
                </Typography>
                <Typography variant="body2" align="center" color="muted">
                  Tambahkan pemeriksaan untuk melihat tren kesehatan
                </Typography>
                <Button
                  title="Tambah Pemeriksaan"
                  onPress={() => {/* Navigate to add checkup */}}
                  style={styles.noDataButton}
                />
              </View>
            </CardContent>
          </Card>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    padding: 40,
  },
  header: {
    marginBottom: 24,
  },
  periodCard: {
    marginBottom: 20,
  },
  periodLabel: {
    marginBottom: 12,
  },
  periodButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  periodButton: {
    flex: 1,
  },
  chartCard: {
    marginBottom: 20,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  chartLegend: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  noDataCard: {
    padding: 20,
  },
  noDataContent: {
    alignItems: 'center',
    gap: 16,
  },
  noDataButton: {
    marginTop: 8,
  },
});
